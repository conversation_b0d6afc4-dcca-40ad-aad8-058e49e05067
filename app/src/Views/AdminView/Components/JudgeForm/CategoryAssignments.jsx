
import { observer } from 'mobx-react';
import { Controller } from 'react-hook-form';
import { Button, FormControlLabel, Checkbox } from '@mui/material';
import Select from 'react-select';
import { useEffect, useState } from 'react';
import { toJS } from 'mobx';
import { fetchEntries } from '../../../../lib/api';

const CategoryAssignments = observer(({
    control,
    watch,
    setValue,
    categories,
    handleNextStep,
    handlePrevStep,
    isEdit,
    currentJudge,
}) => {
    const [availableGroups, setAvailableGroups] = useState([]);
    const [loading, setLoading] = useState(false);

    // Function to fetch groups for a specific category and classification
    const loadGroups = async (categoryId, classificationId) => {
        console.log('Loading groups for:', { categoryId, classificationId });
        setLoading(true);
        try {
            const currentYear = new Date().getFullYear();
            const data = await fetchEntries(categoryId, classificationId, currentYear, false);

            const formattedGroups = data.groups.map(group => {
                // Get the maskedName either directly or from classifications
                let maskedName;
                if (group.maskedName) {
                    maskedName = group.maskedName;
                } else if (group.classifications) {
                    const classification = group.classifications.find(
                        c => c.id.toString() === classificationId.toString()
                    );
                    maskedName = classification?.maskedName;
                }
                
                return {
                    _id: group._id,
                    name: `Group ${maskedName}`,
                    maskedName: maskedName
                };
            });

            // Sort groups by maskedName
            formattedGroups.sort((a, b) => {
                if (!a.maskedName && !b.maskedName) return 0;
                if (!a.maskedName) return 1;
                if (!b.maskedName) return -1;

                const lengthDiff = a.maskedName.length - b.maskedName.length;
                if (lengthDiff === 0) {
                    return a.maskedName.localeCompare(b.maskedName);
                }
                return lengthDiff;
            });

            setAvailableGroups(formattedGroups);
        } catch (error) {
            console.error('Error loading groups:', error);
            setAvailableGroups([]);
        } finally {
            setLoading(false);
        }
    };

    // Watch for changes in the entire form value
    useEffect(() => {
        const subscription = watch((formValues) => {
            if (!formValues?.assigned) return;

            formValues.assigned.forEach((categoryAssignment, categoryIndex) => {
                if (!categoryAssignment?.classifications) return;

                categoryAssignment.classifications.forEach((classification, classIndex) => {
                    if (categoryAssignment.category && classification?.id) {
                        console.log('Triggering loadGroups with:', {
                            category: categoryAssignment.category,
                            classification: classification.id
                        });
                        loadGroups(categoryAssignment.category, classification.id);
                    }
                });
            });
        });

        return () => subscription.unsubscribe();
    }, [watch]);

    // Load initial groups for existing assignments
    useEffect(() => {
        if (isEdit && currentJudge?.assigned) {
            currentJudge.assigned.forEach((categoryAssignment) => {
                if (categoryAssignment.classifications) {
                    categoryAssignment.classifications.forEach((classification) => {
                        if (categoryAssignment.category && classification.id) {
                            loadGroups(categoryAssignment.category, classification.id);
                        }
                    });
                }
            });
        }
    }, [isEdit, currentJudge]);

    useEffect(() => {
        console.log('Edit Mode:', isEdit);
        console.log('Current Judge:', toJS(currentJudge));

        if (isEdit && currentJudge?.assigned) {
            console.log('Setting assigned value:', currentJudge.assigned);

            // Set default value for the assigned field
            setValue('assigned', currentJudge.assigned, {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true
            });
        } else {
            // Initialize with empty array if not editing
            setValue('assigned', [], {
                shouldValidate: true,
                shouldDirty: true,
                shouldTouch: true
            });
        }
    }, [isEdit, currentJudge, setValue]);

    // Add this to verify what's being watched
    const assignedValue = watch('assigned');
    console.log('Current form assigned value:', assignedValue);

    // Placeholder groups for demo - replace with actual groups later
    const mockGroups = [
        { _id: 'group1', name: 'Group A' },
        { _id: 'group2', name: 'Group B' },
        { _id: 'group3', name: 'Group C' },
        { _id: 'group4', name: 'Group D' },
    ];
    console.log(availableGroups)
    return (
        <div className="space-y-6">
            <Controller
                name="assigned"
                control={control}
                defaultValue={[]}
                render={({ field: { value = [], onChange } }) => (
                    <div>
                        {value.map((categoryAssignment, categoryIndex) => (
                            <div key={categoryIndex} className="mb-6 p-4 border rounded">
                                {/* Category Select */}
                                <div className="mb-4">
                                    <label className="block mb-2">Category</label>
                                    <Select
                                        value={categories.find(cat => {
                                            const categoryId = categoryAssignment.category?._id || categoryAssignment.category;
                                            return cat._id === categoryId;
                                        })}
                                        onChange={(selectedCategory) => {
                                            const newValue = [...value];
                                            newValue[categoryIndex] = {
                                                category: selectedCategory._id,
                                                classifications: []
                                            };
                                            onChange(newValue);
                                        }}
                                        options={categories}
                                        getOptionLabel={(option) => option.name}
                                        getOptionValue={(option) => option._id}
                                        maxMenuHeight={200} // Makes the dropdown scrollable with a max height of 200px
                                        className="basic-select"
                                        classNamePrefix="select"
                                    />
                                </div>

                                {/* Classifications */}
                                {categoryAssignment.category && (
                                    <div>
                                        <div className="flex justify-between items-center mb-4">
                                            <label className="block">Classifications</label>
                                            <Button
                                                onClick={() => {
                                                    const selectedCategoryData = categories.find(cat => cat._id === categoryAssignment.category);
                                                    const allClassifications = selectedCategoryData?.classifications?.map(classification => ({
                                                        id: classification._id,
                                                        phase: null,
                                                        groups: []
                                                    })) || [];

                                                    const newValue = [...value];
                                                    newValue[categoryIndex] = {
                                                        ...newValue[categoryIndex],
                                                        classifications: allClassifications
                                                    };
                                                    onChange(newValue);
                                                }}
                                                variant="outlined"
                                                size="small"
                                                className="text-sm"
                                            >
                                                Add All Classifications
                                            </Button>
                                        </div>

                                        {categoryAssignment.classifications.map((classification, classIndex) => (
                                            <div key={classIndex} className="mb-4 p-4 border rounded">
                                                <div className="grid grid-cols-1 gap-4">
                                                    {/* Classification Select */}
                                                    <div>
                                                        <Select
                                                            value={
                                                                categories
                                                                    .find(cat => cat._id === (categoryAssignment.category?._id || categoryAssignment.category))
                                                                    ?.classifications
                                                                    ?.find(c => c._id === (classification.id?._id || classification.id))
                                                            }
                                                            onChange={(selectedClassification) => {
                                                                const newValue = [...value];
                                                                newValue[categoryIndex].classifications[classIndex] = {
                                                                    ...classification,
                                                                    id: selectedClassification._id
                                                                };
                                                                onChange(newValue);
                                                            }}
                                                            options={
                                                                categories
                                                                    .find(cat => cat._id === (categoryAssignment.category?._id || categoryAssignment.category))
                                                                    ?.classifications || []
                                                            }
                                                            getOptionLabel={(option) => option.name}
                                                            getOptionValue={(option) => option._id}
                                                        />
                                                    </div>

                                                    {/* Phase Select */}
                                                    <div>
                                                        <Select
                                                            value={
                                                                classification.phase
                                                                    ? { value: classification.phase, label: `Phase ${classification.phase}` }
                                                                    : null
                                                            }
                                                            onChange={(selectedPhase) => {
                                                                const newValue = [...value];
                                                                newValue[categoryIndex].classifications[classIndex] = {
                                                                    ...classification,
                                                                    phase: selectedPhase?.value || null
                                                                };
                                                                onChange(newValue);
                                                            }}
                                                            options={[
                                                                { value: 1, label: 'Phase 1' },
                                                                { value: 2, label: 'Phase 2' }
                                                            ]}
                                                            isClearable
                                                            placeholder="Select Phase"
                                                        />
                                                        <div className="text-sm text-gray-500 mt-1">
                                                            Leave empty if no phases are required
                                                        </div>
                                                    </div>

                                                    {/* Group Assignment */}
                                                    <div>
                                                        <label className="block mb-2">Assign Specific Groups</label>
                                                        <Select
                                                            isMulti
                                                            isLoading={loading}
                                                            value={availableGroups.filter(group =>
                                                                classification.groups?.some(g =>
                                                                    (g._id || g) === group._id
                                                                )
                                                            )}
                                                            onChange={(selectedGroups) => {
                                                                const newValue = [...value];
                                                                newValue[categoryIndex].classifications[classIndex] = {
                                                                    ...classification,
                                                                    groups: selectedGroups.map(group => group._id)
                                                                };
                                                                onChange(newValue);
                                                            }}
                                                            options={availableGroups}
                                                            getOptionLabel={(option) => option.name}
                                                            getOptionValue={(option) => option._id}
                                                            placeholder={loading ? "Loading groups..." : "Select groups (optional)"}
                                                        />
                                                        <div className="text-sm text-gray-500 mt-1">
                                                            Leave empty to assign all groups in this classification
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Remove Classification Button */}
                                                <Button
                                                    onClick={() => {
                                                        const newValue = [...value];
                                                        newValue[categoryIndex].classifications =
                                                            categoryAssignment.classifications.filter((_, i) => i !== classIndex);
                                                        onChange(newValue);
                                                    }}
                                                    className="mt-2"
                                                    variant="outlined"
                                                    color="error"
                                                    size="small"
                                                >
                                                    Remove Classification
                                                </Button>
                                            </div>
                                        ))}

                                        {/* Add Classification Button */}
                                        <Button
                                            onClick={() => {
                                                const newValue = [...value];
                                                if (!newValue[categoryIndex].classifications) {
                                                    newValue[categoryIndex].classifications = [];
                                                }
                                                newValue[categoryIndex].classifications.push({
                                                    id: '',
                                                    phase: null,
                                                    groups: []
                                                });
                                                onChange(newValue);
                                            }}
                                            variant="outlined"
                                            className="mt-2"
                                        >
                                            Add Classification
                                        </Button>
                                    </div>
                                )}

                                {/* Remove Category Button */}
                                <Button
                                    onClick={() => {
                                        const newValue = value.filter((_, i) => i !== categoryIndex);
                                        onChange(newValue);
                                    }}
                                    className="mt-4"
                                    variant="outlined"
                                    color="error"
                                >
                                    Remove Category
                                </Button>
                            </div>
                        ))}

                        {/* Add Category Button */}
                        <Button
                            onClick={() => {
                                onChange([
                                    ...value,
                                    {
                                        category: '',
                                        classifications: []
                                    }
                                ]);
                            }}
                            variant="contained"
                            className="mt-4"
                            sx={{
                                backgroundColor: '#00773d', // iconGreen color
                                '&:hover': {
                                    backgroundColor: '#357e63' // darker shade for hover
                                }
                            }}
                        >
                            Add Category
                        </Button>
                    </div>
                )}
            />

            <div className="flex justify-between mt-6">
                <Button onClick={handlePrevStep} variant="outlined"
                    sx={{
                        borderColor: '#357e63',
                        color: '#357e63',
                        '&:hover': {
                            borderColor: '#357e63',
                            color: '#357e63'
                        }
                    }}>
                    Previous
                </Button>
                <Button onClick={handleNextStep} variant="contained" sx={{
                    backgroundColor: '#00773d', // iconGreen color
                    '&:hover': {
                        backgroundColor: '#357e63' // darker shade for hover
                    }
                }}>
                    Next
                </Button>
            </div>
        </div>
    );
});

export default CategoryAssignments;
